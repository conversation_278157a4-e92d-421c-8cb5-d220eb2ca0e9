{"version": 2, "builds": [{"src": "api/**/*.js", "use": "@vercel/node"}, {"src": "public/**/*", "use": "@vercel/static"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/agent/(.*)", "dest": "/public/agent/$1"}, {"src": "/(.*\\.(css|js|html|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/public/$1"}, {"src": "/", "dest": "/public/index.html"}, {"src": "/(.*)", "dest": "/public/$1"}], "functions": {"api/**/*.js": {"maxDuration": 30}}, "env": {"NODE_ENV": "production"}}