// Agent Mode API endpoint for Vercel serverless deployment
import { GoogleGenerativeAI } from "@google/generative-ai";
import { config } from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
    console.error('❌ GEMINI_API_KEY not found in environment variables');
}

const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
const model = genAI.getGenerativeModel({
    model: "gemini-2.0-flash-001",
    generationConfig: {
        responseMimeType: "application/json"
    }
});

// Serverless-compatible command execution using Web APIs
async function executeCommand(command) {
    // In serverless environment, we simulate command execution
    // and provide structured responses for common operations
    console.log(`⚡ Simulating command: ${command}`);
    
    // Parse common commands and provide appropriate responses
    if (command.includes('New-Item') && command.includes('-ItemType Directory')) {
        const dirMatch = command.match(/-Path\s+"([^"]+)"/);
        const dirName = dirMatch ? dirMatch[1] : 'project';
        return `Directory created: ${dirName}`;
    }
    
    if (command.includes('Out-File')) {
        const fileMatch = command.match(/-FilePath\s+"([^"]+)"/);
        const fileName = fileMatch ? fileMatch[1] : 'file.txt';
        return `File created: ${fileName}`;
    }
    
    if (command.includes('dir') || command.includes('ls')) {
        return `Directory listing completed`;
    }
    
    return `Command executed: ${command.substring(0, 50)}...`;
}

const TOOLS_MAP = {
    executeCommand: executeCommand,
};

// Function to update status for real-time feedback
function updateStatus(status, message, projectName = null, projectPath = null, toolCall = null, toolResult = null) {
    const statusData = {
        status: status,
        message: message,
        projectName: projectName,
        projectPath: projectPath,
        toolCall: toolCall,
        toolResult: toolResult,
        timestamp: new Date().toISOString(),
        sessionId: 'serverless-' + Date.now()
    };

    console.log(`📊 Status: ${status} - ${message}`);
    return statusData;
}

const SYSTEM_PROMPT = `
You are a helpful AI Assistant designed to resolve user queries in a serverless environment. You work in START, THINK, ACTION, OBSERVE and OUTPUT mode.

In the start phase, user gives a query to you.
Then, you THINK how to resolve that query at least 3-4 times and make sure that all inputs are here.
If there is a need to call a tool, you call an ACTION event with tool and input parameters.
If there is an action call, wait for the OBSERVE that is output of the tool.
Based on the OBSERVE from prev step, you either output or repeat the loop.

IMPORTANT SERVERLESS CONSTRAINTS:
- You are running in a serverless environment with limited file system access
- Focus on creating structured project plans and code templates
- Use simulated command execution for demonstration purposes
- Provide detailed code examples and project structures
- Generate complete, functional code that users can implement

Rules:
- Always wait for next step and wait for the next step
- Always output a single step and wait for the next step
- Output must be strictly JSON
- Only call tool action from Available Tools only
- Strictly follow the output format in JSON
- Focus on creating complete, functional applications
- Provide detailed implementation guidance

Available Tools:
- executeCommand(command): string - Simulates command execution in serverless environment

Example:
START: Create a todo app with HTML, CSS, and JavaScript
THINK: The user wants a complete todo application.
THINK: I need to create the file structure and provide complete code.
ACTION: Call Tool executeCommand("Create project structure")
OBSERVE: Project structure created
THINK: Now I need to provide the complete HTML, CSS, and JavaScript code
OUTPUT: Complete todo app created with all necessary files and functionality.

Output Format:
{ "step": "string", "tool": "string", "input": "string", "content": "string" }
`;

// Main agent processing function
async function processAgentRequest(userQuery) {
    try {
        updateStatus('starting', 'Initializing AI Agent...');

        const messages = [
            {
                role: 'system',
                content: SYSTEM_PROMPT,
            },
            {
                role: 'user',
                content: userQuery,
            }
        ];

        console.log('🚀 Starting AI Agent with query:', userQuery);
        updateStatus('processing', `Processing request: "${userQuery}"`);

        const results = [];
        let iterations = 0;
        const maxIterations = 10; // Prevent infinite loops

        while (iterations < maxIterations) {
            iterations++;
            
            // Convert messages to Gemini format
            let conversationHistory = [];

            // Add system prompt as the first user message
            conversationHistory.push({
                role: 'user',
                parts: [{ text: messages[0].content }]
            });
            conversationHistory.push({
                role: 'model',
                parts: [{ text: 'I understand. I will follow the workflow and respond in JSON format.' }]
            });

            // Add conversation messages
            for (let i = 1; i < messages.length; i++) {
                const msg = messages[i];
                if (msg.role === 'user') {
                    conversationHistory.push({
                        role: 'user',
                        parts: [{ text: msg.content }]
                    });
                } else if (msg.role === 'assistant') {
                    conversationHistory.push({
                        role: 'model',
                        parts: [{ text: msg.content }]
                    });
                }
            }

            const result = await model.generateContent({
                contents: conversationHistory
            });

            const response = await result.response;
            const responseText = response.text();

            messages.push({ 'role': 'assistant', 'content': responseText });

            let parsed_response;
            try {
                parsed_response = JSON.parse(responseText);
            } catch (parseError) {
                console.error('❌ Failed to parse JSON response:', responseText);
                break;
            }

            if (parsed_response.step && parsed_response.step === "think") {
                console.log(`🧠: ${parsed_response.content}`);
                updateStatus('thinking', `AI is thinking: ${parsed_response.content}`);
                results.push({ type: 'think', content: parsed_response.content });
                continue;
            }

            if (parsed_response.step && parsed_response.step === "output") {
                console.log(`🤖: ${parsed_response.content}`);
                updateStatus('completed', 'Task completed successfully!');
                results.push({ type: 'output', content: parsed_response.content });
                break;
            }

            if (parsed_response.step && parsed_response.step === "action") {
                const tool = parsed_response.tool;
                const input = parsed_response.input;

                updateStatus('executing', `Executing: ${tool}`);
                results.push({ type: 'action', tool, input });

                if (!TOOLS_MAP[tool]) {
                    console.error(`❌ Unknown tool: ${tool}`);
                    updateStatus('error', `Unknown tool: ${tool}`);
                    break;
                }

                try {
                    const value = await TOOLS_MAP[tool](input);
                    console.log(`⛏️: Tool Call ${tool}: (${input}) ${value}`);

                    updateStatus('tool_completed', `Completed: ${tool}`);
                    results.push({ type: 'observe', content: value });

                    messages.push({
                        "role": "assistant",
                        "content": JSON.stringify({ "step": "observe", "content": value })
                    });
                } catch (toolError) {
                    console.error(`❌ Tool execution error for ${tool}:`, toolError.message);
                    updateStatus('error', `Tool error: ${tool} - ${toolError.message}`);
                    results.push({ type: 'error', content: toolError.message });
                    break;
                }
                continue;
            }
        }

        return {
            success: true,
            results: results,
            iterations: iterations,
            status: 'completed'
        };

    } catch (error) {
        console.error('❌ Agent processing error:', error.message);
        updateStatus('error', `Agent error: ${error.message}`);
        return {
            success: false,
            error: error.message,
            status: 'error'
        };
    }
}

// Vercel serverless function handler
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
    res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { prompt } = req.body;

        if (!prompt || prompt.trim() === '') {
            return res.status(400).json({ error: 'Prompt is required' });
        }

        console.log(`🤖 Agent mode request:`, prompt);

        // Process the agent request
        const result = await processAgentRequest(prompt);

        res.json({
            success: result.success,
            message: result.success ? 'Agent mode completed successfully!' : 'Agent mode failed',
            prompt: prompt,
            results: result.results,
            iterations: result.iterations,
            status: result.status,
            timestamp: new Date().toISOString(),
            environment: 'serverless'
        });

    } catch (error) {
        console.error('💥 Agent mode error:', error);
        res.status(500).json({
            error: 'Failed to process agent request',
            details: error.message,
            timestamp: new Date().toISOString()
        });
    }
}
