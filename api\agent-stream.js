// Agent Status Streaming API endpoint for Vercel serverless deployment
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

// Vercel serverless function handler for SSE
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        // Set up SSE headers
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });

        // Send initial connection message
        res.write('data: {"type": "connected", "message": "SSE connection established", "environment": "serverless"}\n\n');

        // Send periodic status updates
        const sendStatusUpdate = () => {
            const statusData = {
                type: 'status',
                status: 'active',
                message: 'Agent is running in serverless mode',
                timestamp: new Date().toISOString(),
                environment: 'serverless'
            };
            
            res.write(`data: ${JSON.stringify(statusData)}\n\n`);
        };

        // Send initial status
        sendStatusUpdate();

        // Set up periodic updates (every 5 seconds)
        const interval = setInterval(sendStatusUpdate, 5000);

        // Handle client disconnect
        req.on('close', () => {
            clearInterval(interval);
            console.log('📡 SSE client disconnected');
        });

        req.on('error', (error) => {
            clearInterval(interval);
            console.error('📡 SSE client error:', error);
        });

        // Keep connection alive for up to 25 seconds (Vercel limit is 30s)
        setTimeout(() => {
            clearInterval(interval);
            res.write('data: {"type": "timeout", "message": "Connection timeout"}\n\n');
            res.end();
        }, 25000);

    } catch (error) {
        console.error('SSE streaming error:', error);
        res.status(500).json({
            error: 'Failed to establish SSE connection',
            details: error.message,
            timestamp: new Date().toISOString()
        });
    }
}
