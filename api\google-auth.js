// Google OAuth endpoint for Vercel serverless deployment
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { UserStorage } from './storage.js';
import { FirebaseAuth } from './firebase-auth.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

const userStorage = new UserStorage();
const firebaseAuth = new FirebaseAuth();

// Vercel serverless function handler
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { idToken } = req.body;

        if (!idToken) {
            return res.status(400).json({ error: 'ID token is required' });
        }

        // Verify Firebase token
        const decodedToken = await firebaseAuth.verifyIdToken(idToken);
        const { email, name } = decodedToken;

        if (!email) {
            return res.status(400).json({ error: 'Email not found in token' });
        }

        // Check if user exists
        let user = await userStorage.getUserByEmail(email);

        if (!user) {
            // Create new user
            user = await userStorage.createUser({
                email,
                name: name || email.split('@')[0],
                provider: 'google',
                createdAt: new Date().toISOString()
            });
        }

        // Return user data (frontend will handle session)
        res.json({
            success: true,
            user: {
                id: user.id,
                email: user.email,
                provider: user.provider
            },
            message: 'Google authentication successful'
        });

    } catch (error) {
        console.error('Google auth error:', error);
        res.status(401).json({ 
            error: 'Google authentication failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}
